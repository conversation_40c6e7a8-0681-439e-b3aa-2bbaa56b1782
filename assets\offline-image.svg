<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="offlineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="90" fill="url(#offlineGradient)" opacity="0.1"/>
  
  <!-- Main offline icon -->
  <g transform="translate(100, 100)" filter="url(#glow)">
    <!-- Eye shape -->
    <ellipse cx="0" cy="0" rx="40" ry="25" fill="none" stroke="url(#offlineGradient)" stroke-width="3"/>
    
    <!-- Eye center -->
    <circle cx="0" cy="0" r="8" fill="url(#offlineGradient)"/>
    
    <!-- Slash line -->
    <line x1="-50" y1="-50" x2="50" y2="50" stroke="url(#offlineGradient)" stroke-width="4" stroke-linecap="round"/>
  </g>
  
  <!-- Decorative elements -->
  <g opacity="0.6">
    <!-- Signal waves (crossed out) -->
    <g transform="translate(50, 50)">
      <path d="M0,0 Q10,-10 20,0" fill="none" stroke="url(#offlineGradient)" stroke-width="2" opacity="0.5"/>
      <path d="M0,0 Q15,-15 30,0" fill="none" stroke="url(#offlineGradient)" stroke-width="2" opacity="0.3"/>
      <line x1="-5" y1="-5" x2="35" y2="35" stroke="url(#offlineGradient)" stroke-width="2"/>
    </g>
    
    <g transform="translate(150, 150)">
      <path d="M0,0 Q-10,10 -20,0" fill="none" stroke="url(#offlineGradient)" stroke-width="2" opacity="0.5"/>
      <path d="M0,0 Q-15,15 -30,0" fill="none" stroke="url(#offlineGradient)" stroke-width="2" opacity="0.3"/>
      <line x1="5" y1="5" x2="-35" y2="-35" stroke="url(#offlineGradient)" stroke-width="2"/>
    </g>
  </g>
  
  <!-- Text -->
  <text x="100" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="url(#offlineGradient)" font-weight="bold">
    OFFLINE
  </text>
</svg>
