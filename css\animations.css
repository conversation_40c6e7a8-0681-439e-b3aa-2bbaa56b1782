/* ===== ADVANCED ANIMATIONS & EFFECTS ===== */

/* Hardware Acceleration */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== ENTRANCE ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* ===== SCROLL TRIGGERED ANIMATIONS ===== */
.animate-on-scroll {
  opacity: 0;
  transform: translate3d(0, 50px, 0);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: opacity, transform;
  backface-visibility: hidden;
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  will-change: auto; /* Remove will-change after animation */
}

.animate-on-scroll.delay-1 { transition-delay: 0.1s; }
.animate-on-scroll.delay-2 { transition-delay: 0.2s; }
.animate-on-scroll.delay-3 { transition-delay: 0.3s; }
.animate-on-scroll.delay-4 { transition-delay: 0.4s; }
.animate-on-scroll.delay-5 { transition-delay: 0.5s; }

/* ===== MAGNETIC HOVER EFFECTS ===== */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic:hover {
  transform: scale(1.05);
}

.magnetic-strong:hover {
  transform: scale(1.1) translateY(-5px);
}

/* ===== GLASS MORPHISM EFFECTS ===== */
.glass {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass);
  box-shadow: var(--shadow-glass);
}

.glass-hover {
  transition: all var(--transition-normal);
}

.glass-hover:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
}

/* ===== PARTICLE EFFECTS ===== */
.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--color-primary);
  border-radius: 50%;
  opacity: 0.6;
  animation: particleFloat 20s linear infinite;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100px) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

/* ===== TYPEWRITER EFFECT ===== */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--color-primary);
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--color-primary); }
}

/* ===== RIPPLE EFFECT ===== */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.4s ease-out, height 0.4s ease-out;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}

/* ===== FLOATING ELEMENTS ===== */
.float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.float-delayed {
  animation: float 6s ease-in-out infinite;
  animation-delay: -2s;
}

/* ===== PULSE EFFECTS ===== */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from {
    box-shadow: 0 0 20px var(--color-primary);
  }
  to {
    box-shadow: 0 0 40px var(--color-primary), 0 0 60px var(--color-primary);
  }
}

/* ===== ROTATION EFFECTS ===== */
.rotate-slow {
  animation: rotateSlow 20s linear infinite;
}

@keyframes rotateSlow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotate-hover {
  transition: transform var(--transition-normal);
}

.rotate-hover:hover {
  transform: rotate(5deg);
}

/* ===== SHIMMER EFFECTS ===== */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  /* Ensure left is 0 for translateX to work from the correct origin */
  left: 0; 
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  /* Set transform initially to ensure it's part of the composited layer */
  transform: translateX(-100%); 
  animation: shimmer 2s linear infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* ===== MORPHING EFFECTS ===== */
.morph {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.morph-circle {
  border-radius: 50%;
}

.morph-square {
  border-radius: 0;
}

.morph-rounded {
  border-radius: var(--radius-2xl);
}

/* ===== PARALLAX EFFECTS ===== */
.parallax {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  /* Optimize for smooth scrolling */
  contain: layout style paint;
}

.parallax-slow {
  transform: translateY(var(--parallax-offset-slow, 0));
}

.parallax-medium {
  transform: translateY(var(--parallax-offset-medium, 0));
}

.parallax-fast {
  transform: translateY(var(--parallax-offset-fast, 0));
}

/* ===== STAGGER ANIMATIONS ===== */
.stagger-item {
  opacity: 0;
  transform: translateY(30px);
  animation: staggerFadeIn 0.6s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes staggerFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== MICRO INTERACTIONS ===== */
.micro-bounce {
  transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.micro-bounce:hover {
  transform: scale(1.05);
}

.micro-bounce:active {
  transform: scale(0.95);
}

.micro-slide {
  transition: transform var(--transition-fast);
}

.micro-slide:hover {
  transform: translateX(5px);
}

/* ===== LOADING ANIMATIONS ===== */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dots-loading {
  display: inline-block;
}

.dots-loading::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* ===== REVEAL ANIMATIONS ===== */
.reveal-mask {
  overflow: hidden;
}

.reveal-content {
  transform: translateY(100%);
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.reveal-mask.revealed .reveal-content {
  transform: translateY(0);
}

/* ===== ELASTIC ANIMATIONS ===== */
.elastic {
  animation: elastic 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes elastic {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* ===== SEAMLESS PAGE TRANSITIONS ===== */
.page-transition-container {
  position: relative;
  overflow: hidden;
}

.section-transition {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.section-transition.transitioning-out {
  opacity: 0;
  transform: translateY(-30px) scale(0.98);
  filter: blur(2px);
}

.section-transition.transitioning-in {
  opacity: 0;
  transform: translateY(30px) scale(1.02);
  filter: blur(2px);
}

.section-transition.active {
  opacity: 1;
  transform: translateY(0) scale(1);
  filter: blur(0);
}

/* Morphing section backgrounds */
.section-morph {
  position: relative;
  background: var(--bg-primary);
  transition: background 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.section-morph::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: none;
}

.section-morph.active::before {
  opacity: 0.05;
}

/* Slide-in navigation indicator */
.nav-progress-indicator {
  position: fixed;
  top: 70px;
  left: 0;
  height: 3px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: var(--z-fixed);
}

.nav-progress-indicator.active {
  transform: scaleX(1);
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* Remove will-change after animation */
.animation-complete {
  will-change: auto;
}

/* Initial state for character animations */
.animated-char {
  display: inline-block; /* Allows transforms */
  opacity: 0;
  transform: translateY(20px);
  /* Animation will be applied by JavaScript using WAAPI */
}

/* Quick Press Ripple Effect for Standard Buttons */
@keyframes quickPressRipple {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0.3;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(2.5); /* Adjust scale as needed, relative to ripple's own size */
    opacity: 0;
  }
}

.quick-press-ripple-effect {
  /* Styles are mostly set by JS, but ensure pointer-events if not set by JS always */
  pointer-events: none; 
  position: absolute; /* Ensure JS can override if needed but good default */
  border-radius: 50%; /* Ensure JS can override if needed */
}

/* Old ripple-animation, if still used by .ripple elements via JS */
@keyframes ripple-animation {
    to {
      transform: scale(4); /* This was the existing one for .ripple */
      opacity: 0;
    }
}

/* ===== ENHANCED MICRO-INTERACTIONS ===== */
.enhanced-hover {
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-style: preserve-3d;
}

.enhanced-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  border-radius: inherit;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.enhanced-hover:hover::before {
  opacity: 0.1;
}

.enhanced-hover:hover {
  transform: translateY(-4px) rotateX(5deg);
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
}

/* Breathing animation for important elements */
.breathe {
  animation: breathe 4s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

/* Liquid button morphing */
.liquid-button {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-lg);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.liquid-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.liquid-button:hover::before {
  width: 300px;
  height: 300px;
}

.liquid-button:hover {
  border-radius: 30px;
  transform: scale(1.05);
}

/* Glitch effect for special elements */
.glitch {
  position: relative;
  animation: glitch 2s linear infinite;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s linear infinite reverse;
  color: #ff0000;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s linear infinite reverse;
  color: #00ffff;
  z-index: -2;
}

@keyframes glitch {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

@keyframes glitch-1 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(2px, -2px); }
  40% { transform: translate(-2px, 2px); }
  60% { transform: translate(-2px, -2px); }
  80% { transform: translate(2px, 2px); }
}

@keyframes glitch-2 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, -2px); }
  40% { transform: translate(2px, 2px); }
  60% { transform: translate(2px, -2px); }
  80% { transform: translate(-2px, 2px); }
}
.ripple-effect { /* Class for the old .ripple elements if JS adds it */
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3); /* Default color from old JS */
    transform: scale(0);
    animation: ripple-animation 0.6s linear; /* Default duration from old JS */
    pointer-events: none;
}


/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .parallax {
    transform: none !important;
  }
  
  .particle {
    display: none;
  }
}
